name: ai_essay_grader
description: A Flutter application for AI essay grading.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # For iOS style icons
  cupertino_icons: ^1.0.6

  # For image picking from gallery or camera
  image_picker: ^1.0.7

  # For using camera directly (optional, image_picker can also access camera)
  # camera: ^0.10.5+9 # Check for latest version

  # For rich icons
  font_awesome_flutter: ^10.7.0

  # For SVG images (if you plan to use them)
  flutter_svg: ^2.0.9

  # For state management (optional for mockups, but good for real app)
  # provider: ^6.0.0

  # For network images (if using Unsplash/Pexels directly in code)
  # cached_network_image: ^3.2.3

  # For simulating device frame (optional, for presentation)
  # device_preview: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
  #   - images/a_dot_ham.jpeg

  # An example of adding custom fonts to your application:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700