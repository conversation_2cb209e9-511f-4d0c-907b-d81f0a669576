import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart'; // Required for SystemUiOverlayStyle
import 'package:image_picker/image_picker.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  File? _imageFile;
  final ImagePicker _picker = ImagePicker();
  bool _isProcessing = false;

  Future<void> _pickImage(ImageSource source) async {
    try {
      final pickedFile = await _picker.pickImage(source: source);
      if (pickedFile != null) {
        setState(() {
          _imageFile = File(pickedFile.path);
        });
      } // Corrected comma to brace
    } catch (e) {
      // Handle exceptions, e.g., permission denied
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法选择图片: $e')),
        );
      }
    }
  }

  void _showImageSourceActionSheet(BuildContext context) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: const Text('选择作文图片来源'),
        message: const Text('请选择拍照或从相册中选择'),
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            child: const Text('拍照上传'),
            onPressed: () {
              Navigator.pop(context);
              _pickImage(ImageSource.camera);
            },
          ),
          CupertinoActionSheetAction(
            child: const Text('从相册选择'),
            onPressed: () {
              Navigator.pop(context);
              _pickImage(ImageSource.gallery);
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          isDestructiveAction: true,
          child: const Text('取消'),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
    );
  }

  Future<void> _submitEssay() async {
    if (_imageFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择或拍摄一张作文图片')),
      );
      return;
    }
    setState(() {
      _isProcessing = true;
    });

    // Simulate API call for essay grading
    await Future.delayed(const Duration(seconds: 3));

    // Simulate receiving results
    final String mockResult = "这是一篇优秀的作文，结构清晰，论点明确。建议在段落过渡上可以更自然一些。";
    final int mockScore = 90;

    setState(() {
      _isProcessing = false;
    });

    if (mounted) {
      // Navigate to a results screen or show a dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('批改结果'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text('得分: $mockScore'),
                const SizedBox(height: 10),
                Text('评语: $mockResult'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('好的'),
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _imageFile = null; // Clear image after submission
                });
              },
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI 作文批改', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: Colors.white,
        elevation: 1,
        // Simulating iOS status bar style (light content for dark backgrounds, dark for light)
        systemOverlayStyle: SystemUiOverlayStyle.dark, // For light app bars
      ),
      backgroundColor: Colors.grey[100],
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                '上传作文，智能批改',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 15),
              Text(
                '支持拍照上传或从相册选择清晰的作文图片',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 15, color: Colors.grey[600]),
              ),
              const SizedBox(height: 30),
              GestureDetector(
                onTap: () => _showImageSourceActionSheet(context),
                child: Container(
                  height: 250,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15.0),
                    border: Border.all(
                      color: _imageFile == null ? Colors.blueAccent.withOpacity(0.5) : Colors.green,
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 8,
                        offset: const Offset(0, 4), // changes position of shadow
                      ),
                    ]
                  ),
                  child: _imageFile == null
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            FaIcon(FontAwesomeIcons.cloudArrowUp, size: 60, color: Colors.blueAccent.withOpacity(0.8)),
                            const SizedBox(height: 15),
                            const Text(
                              '点击此处上传作文图片',
                              style: TextStyle(fontSize: 16, color: Colors.blueAccent),
                            ),
                          ],
                        )
                      : ClipRRect(
                          borderRadius: BorderRadius.circular(13.0), // Slightly less than container to show border
                          child: Image.file(
                            _imageFile!,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        ),
                ),
              ),
              if (_imageFile != null)
                Padding(
                  padding: const EdgeInsets.only(top: 15.0, bottom: 5.0),
                  child: TextButton.icon(
                    icon: const Icon(Icons.refresh, size: 18),
                    label: const Text('重新选择图片'),
                    onPressed: () => _showImageSourceActionSheet(context),
                    style: TextButton.styleFrom(foregroundColor: Colors.grey[700]),
                  ),
                ),
              const SizedBox(height: 30),
              _isProcessing
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton.icon(
                      icon: const FaIcon(FontAwesomeIcons.paperPlane, color: Colors.white, size: 18),
                      label: const Text(
                        '提交批改',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
                      ),
                      onPressed: _submitEssay,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blueAccent,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        elevation: 5,
                      ),
                    ),
              const SizedBox(height: 20),
              Text(
                '每次批改将消耗 1 个积分。',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 13, color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      ),
    );
  }
}