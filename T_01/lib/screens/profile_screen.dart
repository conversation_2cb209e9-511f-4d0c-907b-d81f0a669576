import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart'; // Required for SystemUiOverlayStyle
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // Mock user data
  final String _userName = '快乐的小笔头';
  final String _userPhoneNumber = '138****1234'; // Masked phone number
  final String _avatarUrl = 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8dXNlciUyMHByb2ZpbGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80';

  void _logout() {
    // In a real app, clear user session and navigate to login
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: const Text('退出登录'),
        content: const Text('您确定要退出当前账号吗？'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: const Text('取消'),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('确定退出'),
            onPressed: () {
              Navigator.pop(context); // Close dialog
              // Navigate to login screen, ensuring no back navigation to main app
              Navigator.of(context, rootNavigator: true).pushNamedAndRemoveUntil('/login', (Route<dynamic> route) => false);
            },
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于我们'),
        content: const SingleChildScrollView(
          child: ListBody(
            children: <Widget>[
              Text('AI 作文批改 App'),
              SizedBox(height: 8),
              Text('版本: 1.0.0 (原型)'),
              SizedBox(height: 8),
              Text('致力于为家长和学生提供便捷、高效的作文批改服务。'),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: const Text('好的'),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }

  void _contactSupport() {
     ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('客服功能正在开发中... (模拟)')),
    );
    // In a real app, this could open a chat, email, or phone call
  }

  Widget _buildProfileOption(BuildContext context, {required IconData icon, required String title, required VoidCallback onTap, bool isDestructive = false}) {
    return Card(
      elevation: 0.5,
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
      child: ListTile(
        leading: FaIcon(icon, color: isDestructive ? Colors.redAccent : Theme.of(context).primaryColor, size: 20),
        title: Text(title, style: TextStyle(fontSize: 16, color: isDestructive ? Colors.redAccent : Colors.black87)),
        trailing: Icon(CupertinoIcons.chevron_forward, size: 18, color: Colors.grey[400]),
        onTap: onTap,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: Colors.white,
        elevation: 1,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            // User Info Header
            Container(
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.15),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ]
              ),
              child: Row(
                children: <Widget>[
                  CircleAvatar(
                    radius: 35,
                    backgroundImage: NetworkImage(_avatarUrl),
                    backgroundColor: Colors.grey[200],
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          _userName,
                          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black87),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          _userPhoneNumber,
                          style: TextStyle(fontSize: 15, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  // Icon(CupertinoIcons.pencil_circle, color: Colors.grey[400]), // Edit icon (optional)
                ],
              ),
            ),
            const SizedBox(height: 25),

            // Options List
            _buildProfileOption(
              context,
              icon: FontAwesomeIcons.headset,
              title: '联系客服',
              onTap: _contactSupport,
            ),
            _buildProfileOption(
              context,
              icon: FontAwesomeIcons.circleInfo,
              title: '关于我们',
              onTap: _showAboutDialog,
            ),
            _buildProfileOption(
              context,
              icon: FontAwesomeIcons.fileContract, // Example for terms
              title: '用户协议与隐私政策',
              onTap: () {
                 ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('正在打开协议... (模拟)')),
                );
              },
            ),
            const SizedBox(height: 20),
            _buildProfileOption(
              context,
              icon: FontAwesomeIcons.arrowRightFromBracket,
              title: '退出登录',
              onTap: _logout,
              isDestructive: true,
            ),
            const SizedBox(height: 30),
            Text(
              'App Version 1.0.0 (Prototype)',
              style: TextStyle(color: Colors.grey[400], fontSize: 12),
            )
          ],
        ),
      ),
    );
  }
}