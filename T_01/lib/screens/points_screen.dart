import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart'; // Required for SystemUiOverlayStyle
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class PointsPackage {
  final int points;
  final double price;
  final String? bonus;

  PointsPackage({required this.points, required this.price, this.bonus});
}

class PointsScreen extends StatefulWidget {
  const PointsScreen({super.key});

  @override
  State<PointsScreen> createState() => _PointsScreenState();
}

class _PointsScreenState extends State<PointsScreen> {
  int _currentPoints = 2; // Mock current points

  final List<PointsPackage> _packages = [
    PointsPackage(points: 10, price: 6.00, bonus: '新手推荐'),
    PointsPackage(points: 50, price: 28.00, bonus: '加赠5积分'),
    PointsPackage(points: 100, price: 50.00, bonus: '加赠15积分'),
    PointsPackage(points: 200, price: 98.00, bonus: '加赠40积分'),
  ];

  void _showPaymentConfirmation(PointsPackage package) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: const Text('确认支付'),
        content: Text('您将支付 ¥${package.price.toStringAsFixed(2)} 来购买 ${package.points} 积分。\n\n(模拟) 是否继续使用微信支付？'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: const Text('取消'),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            child: const Text('确认支付'),
            onPressed: () {
              Navigator.pop(context);
              // Simulate successful payment and points update
              setState(() {
                _currentPoints += package.points;
                if (package.bonus != null && package.bonus!.contains('加赠')){
                    // Extract bonus points, e.g., "加赠5积分" -> 5
                    final bonusPoints = int.tryParse(package.bonus!.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
                    _currentPoints += bonusPoints;
                }
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('充值成功！${package.points} 积分已到账。当前总积分: $_currentPoints'),
                  backgroundColor: Colors.green,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('积分充值', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: Colors.white,
        elevation: 1,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      backgroundColor: Colors.grey[100],
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            // Current Points Display
            Container(
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blueAccent, Colors.lightBlue.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blueAccent.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  )
                ]
              ),
              child: Column(
                children: [
                  const Text(
                    '当前可用积分',
                    style: TextStyle(fontSize: 18, color: Colors.white70),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$_currentPoints',
                    style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    '每次作文批改消耗 1 积分',
                    style: TextStyle(fontSize: 13, color: Colors.white.withOpacity(0.9)),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 30),
            Text(
              '选择充值套餐',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 15),
            // Recharge Packages
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(), // to disable GridView's scrolling
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 15.0,
                mainAxisSpacing: 15.0,
                childAspectRatio: 0.9, // Adjust for better layout
              ),
              itemCount: _packages.length,
              itemBuilder: (context, index) {
                final package = _packages[index];
                bool isRecommended = package.bonus == '新手推荐';
                return Card(
                  elevation: 3.0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    side: isRecommended ? BorderSide(color: Colors.orangeAccent, width: 2) : BorderSide.none,
                  ),
                  child: InkWell(
                    onTap: () => _showPaymentConfirmation(package),
                    borderRadius: BorderRadius.circular(12.0),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Column(
                            children: [
                              if (package.bonus != null)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                                  decoration: BoxDecoration(
                                    color: isRecommended ? Colors.orangeAccent : Colors.green.shade100,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    package.bonus!,
                                    style: TextStyle(fontSize: 11, color: isRecommended ? Colors.white : Colors.green.shade800, fontWeight: FontWeight.bold),
                                  ),
                                ),
                              const SizedBox(height: 10),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text(
                                    '${package.points}',
                                    style: TextStyle(
                                      fontSize: 36,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blueAccent,
                                    ),
                                  ),
                                  const Text(
                                    ' 积分',
                                    style: TextStyle(fontSize: 14, color: Colors.blueAccent),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          Text(
                            '¥${package.price.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FaIcon(FontAwesomeIcons.shieldHalved, color: Colors.green.shade600, size: 16),
                const SizedBox(width: 8),
                Text(
                  '安全支付，积分即时到账',
                  style: TextStyle(fontSize: 13, color: Colors.grey[600]),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}