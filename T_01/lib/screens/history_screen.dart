import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart'; // For Cupertino icons
import 'package:flutter/services.dart'; // Required for SystemUiOverlayStyle
import 'package:font_awesome_flutter/font_awesome_flutter.dart'; // For FontAwesome icons

// Mock data model for history item
class HistoryItem {
  final String id;
  final String title;
  final DateTime date;
  final String score;
  final String? thumbnailUrl; // Optional: for image preview
  final String feedbackSummary;

  HistoryItem({
    required this.id,
    required this.title,
    required this.date,
    required this.score,
    this.thumbnailUrl,
    required this.feedbackSummary,
  });
}

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  // Mock data for demonstration
  final List<HistoryItem> _historyItems = [
    HistoryItem(
      id: '1',
      title: '我的暑假生活',
      date: DateTime.now().subtract(const Duration(days: 2)),
      score: '85分',
      thumbnailUrl: 'https://images.unsplash.com/photo-1517842645767-c639042777db?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZXNzYXl8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=100&q=60',
      feedbackSummary: '整体不错，注意段落连接。'
    ),
    HistoryItem(
      id: '2',
      title: '一次难忘的旅行',
      date: DateTime.now().subtract(const Duration(days: 5)),
      score: '92分',
      // thumbnailUrl: null, // No image example
      feedbackSummary: '描写生动，情感真挚。'
    ),
    HistoryItem(
      id: '3',
      title: '环保小建议',
      date: DateTime.now().subtract(const Duration(days: 10)),
      score: '78分',
      thumbnailUrl: 'https://images.unsplash.com/photo-1455390587153-563c71008493?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8ZXNzYXl8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=100&q=60',
      feedbackSummary: '观点新颖，但论据稍显不足。'
    ),
     HistoryItem(
      id: '4',
      title: '给妈妈的一封信',
      date: DateTime.now().subtract(const Duration(days: 15)),
      score: '95分',
      thumbnailUrl: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8d3JpdGluZ3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=100&q=60',
      feedbackSummary: '感情充沛，表达流畅。'
    ),
  ];

  void _viewHistoryDetail(HistoryItem item) {
    // Navigate to a detailed view or show a dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(item.title),
        content: SingleChildScrollView(
          child: ListBody(
            children: <Widget>[
              Text('日期: ${item.date.year}-${item.date.month}-${item.date.day}'),
              const SizedBox(height: 8),
              Text('得分: ${item.score}'),
              const SizedBox(height: 8),
              Text('简评: ${item.feedbackSummary}'),
              if (item.thumbnailUrl != null)
                Padding(
                  padding: const EdgeInsets.only(top: 15.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: Image.network(item.thumbnailUrl!, height: 150, width: double.infinity, fit: BoxFit.cover)
                  ),
                ),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: const Text('关闭'),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('批改历史', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: Colors.white,
        elevation: 1,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      backgroundColor: Colors.grey[100],
      body: _historyItems.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FaIcon(FontAwesomeIcons.folderOpen, size: 60, color: Colors.grey[400]),
                  const SizedBox(height: 20),
                  Text(
                    '暂无批改记录',
                    style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    '提交的作文档案会在这里显示哦',
                    style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(10.0),
              itemCount: _historyItems.length,
              itemBuilder: (context, index) {
                final item = _historyItems[index];
                return Card(
                  elevation: 2.0,
                  margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 5.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(15.0),
                    leading: item.thumbnailUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child: Image.network(
                              item.thumbnailUrl!,
                              width: 50,
                              height: 50,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => 
                                Container(width: 50, height: 50, color: Colors.grey[200], child: Icon(CupertinoIcons.doc_text, color: Colors.grey[400])),
                            ),
                          )
                        : Container(
                            width: 50, 
                            height: 50, 
                            decoration: BoxDecoration(
                              color: Colors.blueAccent.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8.0)
                            ),
                            child: Icon(CupertinoIcons.doc_text_fill, color: Colors.blueAccent, size: 28)
                          ),
                    title: Text(item.title, style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16)),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Text('日期: ${item.date.year}-${item.date.month}-${item.date.day}', style: TextStyle(fontSize: 13, color: Colors.grey[600])),
                        const SizedBox(height: 3),
                        Text('得分: ${item.score}', style: TextStyle(fontSize: 14, color: Colors.green[700], fontWeight: FontWeight.bold)),
                      ],
                    ),
                    trailing: const Icon(CupertinoIcons.chevron_forward, color: Colors.grey),
                    onTap: () => _viewHistoryDetail(item),
                  ),
                );
              },
            ),
    );
  }
}