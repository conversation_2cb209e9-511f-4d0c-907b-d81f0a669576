import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart'; // For Cupertino icons
import 'package:font_awesome_flutter/font_awesome_flutter.dart'; // For FontAwesome icons

// Placeholder screens - will be created in separate files later
import 'screens/home_screen.dart';
import 'screens/history_screen.dart';
import 'screens/points_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/login_screen.dart'; // Login screen

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AI 作文批改',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        // Simulate iOS page transitions
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
            TargetPlatform.android: CupertinoPageTransitionsBuilder(), // Or use FadeUpwardsPageTransitionsBuilder
          },
        ),
      ),
      // Start with LoginScreen, then navigate to MainScreen after login
      home: const LoginScreen(), 
      routes: {
        '/main': (context) => const MainScreen(),
        // Potentially other routes if needed
      },
      debugShowCheckedModeBanner: false, // Remove debug banner
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  static const List<Widget> _widgetOptions = <Widget>[
    HomeScreen(),
    HistoryScreen(),
    PointsScreen(),
    ProfileScreen(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Simulate iPhone 15 Pro screen size and rounded corners
    // This is a visual simulation for the mockup. Actual device properties are handled by Flutter.
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    // iPhone 15 Pro: 393 x 852 points
    // We'll use a fixed aspect ratio for the mockup container if needed, but generally let Flutter handle responsiveness.

    return Scaffold(
      body: Center(
        child: _widgetOptions.elementAt(_selectedIndex),
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(CupertinoIcons.home),
            label: '首页',
          ),
          BottomNavigationBarItem(
            icon: Icon(CupertinoIcons.list_bullet),
            label: '批改历史',
          ),
          BottomNavigationBarItem(
            icon: FaIcon(FontAwesomeIcons.coins), // Using FontAwesome for a more specific icon
            label: '积分充值',
          ),
          BottomNavigationBarItem(
            icon: Icon(CupertinoIcons.person),
            label: '我的',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.blueAccent,
        unselectedItemColor: Colors.grey,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed, // Ensures all labels are visible
        showUnselectedLabels: true,
      ),
    );
  }
}